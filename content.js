// Cookie monitoring script that runs on every page load
console.log('Cookie monitor extension loaded on:', window.location.href);

// Check if cookieStore API is available
if ('cookieStore' in window) {
    try {
        // Add event listener for cookie changes
        cookieStore.addEventListener('change', ({changed}) => {
            for (const {name, value} of changed) {
                console.log(`<PERSON><PERSON> changed: ${name} was set to ${value}`);
            }
        });
        
        console.log('Cookie change listener successfully registered');
    } catch (error) {
        console.error('Error setting up cookie change listener:', error);
    }
} else {
    console.warn('cookieStore API is not available in this browser/context');
    
    // Fallback: Monitor document.cookie changes using a MutationObserver approach
    let lastCookieString = document.cookie;
    
    const checkCookieChanges = () => {
        const currentCookieString = document.cookie;
        if (currentCookieString !== lastCookieString) {
            console.log('Document.cookie changed from:', lastCookieString);
            console.log('Document.cookie changed to:', currentCookieString);
            lastCookieString = currentCookieString;
        }
    };
    
    // Check for cookie changes periodically as a fallback
    setInterval(checkCookieChanges, 1000);
    
    // Also check when the page becomes visible (in case cookies changed while tab was hidden)
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
            checkCookieChanges();
        }
    });
}

// Log initial cookies when page loads
console.log('Initial cookies on page load:', document.cookie);
