// Cookie monitoring script that runs on every page load
console.log('Cookie monitor extension loaded on:', window.location.href);

// Parse cookies into a map for comparison
function parseCookies(cookieString) {
    const cookies = new Map();
    if (!cookieString) return cookies;

    cookieString.split(';').forEach(cookie => {
        const [name, ...valueParts] = cookie.trim().split('=');
        if (name) {
            cookies.set(name, valueParts.join('=') || '');
        }
    });
    return cookies;
}

// Compare cookie maps and log changes
function compareCookies(oldCookies, newCookies) {
    // Check for new or changed cookies
    for (const [name, value] of newCookies) {
        if (!oldCookies.has(name)) {
            console.log(`🍪 NEW COOKIE: ${name} = ${value}`);
        } else if (oldCookies.get(name) !== value) {
            console.log(`🍪 CHANGED COOKIE: ${name} changed from "${oldCookies.get(name)}" to "${value}"`);
        }
    }

    // Check for deleted cookies
    for (const [name, value] of oldCookies) {
        if (!newCookies.has(name)) {
            console.log(`🍪 DELETED COOKIE: ${name} (was "${value}")`);
        }
    }
}

// Store initial cookie state
let lastCookies = parseCookies(document.cookie);
console.log('Initial cookies on page load:', document.cookie);

// Monitor document.cookie changes with high frequency polling
const checkCookieChanges = () => {
    const currentCookieString = document.cookie;
    const currentCookies = parseCookies(currentCookieString);

    // Compare with previous state
    if (currentCookies.size !== lastCookies.size ||
        JSON.stringify([...currentCookies]) !== JSON.stringify([...lastCookies])) {

        console.log('🔍 Cookie change detected!');
        compareCookies(lastCookies, currentCookies);
        lastCookies = currentCookies;
    }
};

// High-frequency polling to catch rapid cookie changes
setInterval(checkCookieChanges, 100); // Check every 100ms

// Also use cookieStore API if available (for additional coverage)
if ('cookieStore' in window) {
    try {
        cookieStore.addEventListener('change', ({changed, deleted}) => {
            console.log('🍪 CookieStore API detected changes:');
            for (const {name, value} of changed) {
                console.log(`  CHANGED via CookieStore: ${name} = ${value}`);
            }
            for (const {name} of deleted) {
                console.log(`  DELETED via CookieStore: ${name}`);
            }
        });
        console.log('CookieStore API listener registered');
    } catch (error) {
        console.error('Error setting up cookieStore listener:', error);
    }
}

// Override document.cookie setter to catch direct assignments
const originalCookieDescriptor = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie') ||
                                Object.getOwnPropertyDescriptor(HTMLDocument.prototype, 'cookie');

if (originalCookieDescriptor && originalCookieDescriptor.set) {
    Object.defineProperty(document, 'cookie', {
        get: originalCookieDescriptor.get,
        set: function(value) {
            console.log('🍪 document.cookie assignment detected:', value);
            const result = originalCookieDescriptor.set.call(this, value);
            // Trigger immediate check after a brief delay to allow the cookie to be set
            setTimeout(checkCookieChanges, 10);
            return result;
        },
        configurable: true
    });
    console.log('document.cookie setter override installed');
}

// Monitor for network requests that might set cookies via Set-Cookie headers
const originalFetch = window.fetch;
window.fetch = function(...args) {
    return originalFetch.apply(this, args).then(response => {
        if (response.headers.get('set-cookie')) {
            console.log('🍪 Set-Cookie header detected in fetch response');
            setTimeout(checkCookieChanges, 10);
        }
        return response;
    });
};

// Monitor XMLHttpRequest for Set-Cookie headers
const originalXHROpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function(...args) {
    this.addEventListener('readystatechange', function() {
        if (this.readyState === 4) {
            const setCookieHeader = this.getResponseHeader('set-cookie');
            if (setCookieHeader) {
                console.log('🍪 Set-Cookie header detected in XHR response');
                setTimeout(checkCookieChanges, 10);
            }
        }
    });
    return originalXHROpen.apply(this, args);
};

console.log('🍪 Comprehensive cookie monitoring active!');
