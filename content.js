// Cookie monitoring script that runs on every page load
console.log('Cookie monitor extension loaded on:', window.location.href);

// Parse cookies into a map for comparison
function parseCookies(cookieString) {
    const cookies = new Map();
    if (!cookieString) return cookies;

    cookieString.split(';').forEach(cookie => {
        const [name, ...valueParts] = cookie.trim().split('=');
        if (name) {
            cookies.set(name, valueParts.join('=') || '');
        }
    });
    return cookies;
}

// Compare cookie maps and log changes
function compareCookies(oldCookies, newCookies) {
    // Check for new or changed cookies
    for (const [name, value] of newCookies) {
        if (!oldCookies.has(name)) {
            console.log(`🍪 NEW COOKIE: ${name} = ${value}`);
        } else if (oldCookies.get(name) !== value) {
            console.log(`🍪 CHANGED COOKIE: ${name} changed from "${oldCookies.get(name)}" to "${value}"`);
        }
    }

    // Check for deleted cookies
    for (const [name, value] of oldCookies) {
        if (!newCookies.has(name)) {
            console.log(`🍪 DELETED COOKIE: ${name} (was "${value}")`);
        }
    }
}

// Store initial cookie state from both document.cookie and chrome.cookies API
let lastCookies = parseCookies(document.cookie);
let lastAllCookies = new Map();

// Function to get all cookies including HttpOnly ones via background script
async function getAllCookies() {
    try {
        const url = window.location.href;
        const response = await chrome.runtime.sendMessage({
            action: 'getAllCookies',
            url: url
        });

        if (response.success) {
            const cookieMap = new Map();
            response.cookies.forEach(cookie => {
                cookieMap.set(cookie.name, {
                    value: cookie.value,
                    httpOnly: cookie.httpOnly,
                    secure: cookie.secure,
                    sameSite: cookie.sameSite,
                    domain: cookie.domain,
                    path: cookie.path
                });
            });
            return cookieMap;
        } else {
            console.error('Error getting cookies from background script:', response.error);
            return new Map();
        }
    } catch (error) {
        console.error('Error communicating with background script:', error);
        return new Map();
    }
}

// Compare all cookies (including HttpOnly)
function compareAllCookies(oldCookies, newCookies) {
    // Check for new or changed cookies
    for (const [name, cookieData] of newCookies) {
        if (!oldCookies.has(name)) {
            const httpOnlyFlag = cookieData.httpOnly ? ' [HttpOnly]' : '';
            const secureFlag = cookieData.secure ? ' [Secure]' : '';
            console.log(`🍪 NEW COOKIE: ${name} = ${cookieData.value}${httpOnlyFlag}${secureFlag}`);
        } else {
            const oldData = oldCookies.get(name);
            if (oldData.value !== cookieData.value) {
                const httpOnlyFlag = cookieData.httpOnly ? ' [HttpOnly]' : '';
                console.log(`🍪 CHANGED COOKIE: ${name} changed from "${oldData.value}" to "${cookieData.value}"${httpOnlyFlag}`);
            }
        }
    }

    // Check for deleted cookies
    for (const [name, cookieData] of oldCookies) {
        if (!newCookies.has(name)) {
            const httpOnlyFlag = cookieData.httpOnly ? ' [HttpOnly]' : '';
            console.log(`🍪 DELETED COOKIE: ${name} (was "${cookieData.value}")${httpOnlyFlag}`);
        }
    }
}

// Initialize and log all cookies including HttpOnly
getAllCookies().then(allCookies => {
    lastAllCookies = allCookies;
    console.log('📋 ALL COOKIES (including HttpOnly) on page load:');
    for (const [name, data] of allCookies) {
        const flags = [];
        if (data.httpOnly) flags.push('HttpOnly');
        if (data.secure) flags.push('Secure');
        if (data.sameSite) flags.push(`SameSite=${data.sameSite}`);
        const flagStr = flags.length > 0 ? ` [${flags.join(', ')}]` : '';
        console.log(`  ${name} = ${data.value}${flagStr}`);
    }
    console.log('📋 document.cookie (accessible only):', document.cookie);
});

// Monitor document.cookie changes with high frequency polling
const checkCookieChanges = () => {
    const currentCookieString = document.cookie;
    const currentCookies = parseCookies(currentCookieString);

    // Compare with previous state
    if (currentCookies.size !== lastCookies.size ||
        JSON.stringify([...currentCookies]) !== JSON.stringify([...lastCookies])) {

        console.log('🔍 Document.cookie change detected!');
        compareCookies(lastCookies, currentCookies);
        lastCookies = currentCookies;
    }
};

// Monitor ALL cookies (including HttpOnly) with high frequency polling
const checkAllCookieChanges = async () => {
    const currentAllCookies = await getAllCookies();

    // Compare with previous state
    if (currentAllCookies.size !== lastAllCookies.size ||
        JSON.stringify([...currentAllCookies]) !== JSON.stringify([...lastAllCookies])) {

        console.log('🔍 ALL COOKIES change detected (including HttpOnly)!');
        compareAllCookies(lastAllCookies, currentAllCookies);
        lastAllCookies = currentAllCookies;
    }
};

// High-frequency polling to catch rapid cookie changes
setInterval(checkCookieChanges, 100); // Check document.cookie every 100ms
setInterval(checkAllCookieChanges, 500); // Check all cookies every 500ms (less frequent due to API overhead)

// Also use cookieStore API if available (for additional coverage)
if ('cookieStore' in window) {
    try {
        cookieStore.addEventListener('change', ({changed, deleted}) => {
            console.log('🍪 CookieStore API detected changes:');
            for (const {name, value} of changed) {
                console.log(`  CHANGED via CookieStore: ${name} = ${value}`);
            }
            for (const {name} of deleted) {
                console.log(`  DELETED via CookieStore: ${name}`);
            }
        });
        console.log('CookieStore API listener registered');
    } catch (error) {
        console.error('Error setting up cookieStore listener:', error);
    }
}

// Override document.cookie setter to catch direct assignments
const originalCookieDescriptor = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie') ||
                                Object.getOwnPropertyDescriptor(HTMLDocument.prototype, 'cookie');

if (originalCookieDescriptor && originalCookieDescriptor.set) {
    Object.defineProperty(document, 'cookie', {
        get: originalCookieDescriptor.get,
        set: function(value) {
            console.log('🍪 document.cookie assignment detected:', value);
            const result = originalCookieDescriptor.set.call(this, value);
            // Trigger immediate check after a brief delay to allow the cookie to be set
            setTimeout(checkCookieChanges, 10);
            setTimeout(checkAllCookieChanges, 50); // Also check all cookies
            return result;
        },
        configurable: true
    });
    console.log('document.cookie setter override installed');
}

// Monitor for network requests that might set cookies via Set-Cookie headers
const originalFetch = window.fetch;
window.fetch = function(...args) {
    return originalFetch.apply(this, args).then(response => {
        if (response.headers.get('set-cookie')) {
            console.log('🍪 Set-Cookie header detected in fetch response');
            setTimeout(checkCookieChanges, 10);
            setTimeout(checkAllCookieChanges, 50); // Also check all cookies including HttpOnly
        }
        return response;
    });
};

// Monitor XMLHttpRequest for Set-Cookie headers
const originalXHROpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function(...args) {
    this.addEventListener('readystatechange', function() {
        if (this.readyState === 4) {
            const setCookieHeader = this.getResponseHeader('set-cookie');
            if (setCookieHeader) {
                console.log('🍪 Set-Cookie header detected in XHR response');
                setTimeout(checkCookieChanges, 10);
                setTimeout(checkAllCookieChanges, 50); // Also check all cookies including HttpOnly
            }
        }
    });
    return originalXHROpen.apply(this, args);
};

// Listen for cookie change messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'cookieChanged') {
        const { cookie, removed } = message.changeInfo;
        const httpOnlyFlag = cookie.httpOnly ? ' [HttpOnly]' : '';
        const secureFlag = cookie.secure ? ' [Secure]' : '';

        if (removed) {
            console.log(`🍪 CHROME API - DELETED: ${cookie.name}${httpOnlyFlag}${secureFlag}`);
        } else {
            console.log(`🍪 CHROME API - SET: ${cookie.name} = ${cookie.value}${httpOnlyFlag}${secureFlag}`);
        }

        // Update our tracking
        setTimeout(checkAllCookieChanges, 10);
    }
});

console.log('Chrome cookies API change listener registered via background script');

console.log('🍪 Comprehensive cookie monitoring active!');
