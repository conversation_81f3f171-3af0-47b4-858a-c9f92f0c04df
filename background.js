// Background script to handle Chrome cookies API
console.log('<PERSON><PERSON> monitor background script loaded');

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'getAllCookies') {
        // Get all cookies for the requesting tab's URL
        chrome.cookies.getAll({ url: request.url })
            .then(cookies => {
                const cookieData = cookies.map(cookie => ({
                    name: cookie.name,
                    value: cookie.value,
                    httpOnly: cookie.httpOnly,
                    secure: cookie.secure,
                    sameSite: cookie.sameSite,
                    domain: cookie.domain,
                    path: cookie.path,
                    expirationDate: cookie.expirationDate
                }));
                sendResponse({ success: true, cookies: cookieData });
            })
            .catch(error => {
                console.error('Error getting cookies:', error);
                sendResponse({ success: false, error: error.message });
            });
        
        // Return true to indicate we'll send a response asynchronously
        return true;
    }
});

// Listen for cookie changes and notify content scripts
chrome.cookies.onChanged.addListener((changeInfo) => {
    const cookie = changeInfo.cookie;
    
    // Get all tabs to notify them of cookie changes
    chrome.tabs.query({}, (tabs) => {
        tabs.forEach(tab => {
            // Check if this cookie change is relevant to the tab's URL
            if (tab.url && (tab.url.includes(cookie.domain) || cookie.domain.startsWith('.'))) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'cookieChanged',
                    changeInfo: {
                        cookie: {
                            name: cookie.name,
                            value: cookie.value,
                            httpOnly: cookie.httpOnly,
                            secure: cookie.secure,
                            sameSite: cookie.sameSite,
                            domain: cookie.domain,
                            path: cookie.path
                        },
                        removed: changeInfo.removed
                    }
                }).catch(() => {
                    // Ignore errors for tabs that don't have our content script
                });
            }
        });
    });
});

console.log('Background script cookie monitoring active');
