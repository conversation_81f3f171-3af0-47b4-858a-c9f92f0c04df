{"manifest_version": 3, "name": "Cookie Monitor Extension", "version": "1.0", "description": "A Chrome extension that monitors cookie changes on every page load.", "permissions": ["cookies"], "host_permissions": ["<all_urls>"], "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_start"}], "action": {"default_popup": "popup.html", "default_icon": {"16": "images/icon-16.png", "48": "images/icon-48.png", "128": "images/icon-128.png"}}, "icons": {"16": "images/icon-16.png", "48": "images/icon-48.png", "128": "images/icon-128.png"}}